import asyncio
from asyncio import AbstractEventLoop
from sanic import Sanic
from sanic.response import json
from beezer.plugins import load_plugins  # 导入插件加载函数
from beezer.config import YamlConfig
from beezer.app import app
from loguru import logger
from beezer.type_model import InboundTypes, OutBoundModel
from beezer.license.main import init_system, do_verify
from beezer.process import start_process
from beezer.apps import commands  # Import commands module
from beezer.plugins.flows._basic import BasicFlow
import argparse
import os
import math
import time
from datetime import datetime
from typing import List, Dict, Any
from beezer.apps.commands import SupervisorAppConfig

DEBUG = os.getenv("DEBUG", False)

# 全局字典，用于存储插件状态和数据
plugin_status = {}
plugin_data = {}  # 存储插件数据，格式: {plugin_id: [data_entries]}
MAX_DATA_ENTRIES_PER_PLUGIN = 1000  # 每个插件最多保存的数据条目数

# 在应用启动时加载插件
load_plugins(app.config)


def distribute_flows(
    flows: List[dict], max_flows_per_process: int = 1
) -> List[List[dict]]:
    """Distribute flows across processes, ensuring each process has at most max_flows_per_process flows"""
    num_flows = len(flows)
    if num_flows == 0:
        return []

    # Calculate number of processes needed based on max_flows_per_process
    num_processes = math.ceil(num_flows / max_flows_per_process)

    # Distribute flows across processes
    processes = []
    remaining_flows = flows.copy()

    while remaining_flows:
        # Take up to max_flows_per_process flows for this process
        process_flows = remaining_flows[:max_flows_per_process]
        processes.append(process_flows)
        # Remove the allocated flows from remaining_flows
        remaining_flows = remaining_flows[max_flows_per_process:]

    return processes


@app.main_process_start
async def main_process_start(app: Sanic, loop: AbstractEventLoop):
    logger.info("main start")
    # return
    init_system()
    # First start all configured apps
    for idx, app_config in enumerate(app.config.apps):
        try:
            app_type = commands.AppType(app_config.type)
            app_command = commands.registry.get_app_config(app_type)
            if not app_command:
                logger.warning(f"Unknown app type: {app_config.type}")
                continue

            supervisor_config = app_command["supervisor"]
            # 创建新的args列表，避免修改原始列表
            supervisor_config.args = supervisor_config.args.copy()
            supervisor_config.args.extend(app_config.args)

            command = supervisor_config.get_command()
            # print(command)
            await start_process(app_config.name, command, supervisor_config)
        except ValueError as e:
            logger.error(f"Invalid app type: {app_config.type}, error: {e}")
            continue
        except Exception as e:
            logger.error(f"Failed to start process {app_config.name}: {e}")
            continue

    # Now distribute and start flow processes
    flow_processes = distribute_flows(app.config.flows)
    for process_idx, process_flows in enumerate(flow_processes):
        flow_args = []
        for flow in process_flows:
            flow_args.append(f"--flow={flow.name}")

        # Start flow worker process
        process_name = f"flow_worker_{process_idx}"
        worker_script = "/app/worker"
        process_command = f"{worker_script} {' '.join(flow_args)}"

        supervisor_config = {
            "name": process_name,
            "script": worker_script,
            # "args": flow_args,
            "kwargs": {
                "command": process_command,
            },
            "interpreter": None,
            "autorestart": True,
            "max_memory_restart": "260M",
            "instances": 1,
            "watch": False,
            "type": commands.AppType.CUSTOM,
        }

        try:
            supervisor_config = SupervisorAppConfig(**supervisor_config)
            # print(supervisor_config)
            await start_process(process_name, process_command, supervisor_config)
            logger.info(
                f"Started flow worker process {process_name} with flows: {[f.name for f in process_flows]}"
            )
        except Exception as e:
            logger.error(f"Failed to start flow worker process {process_name}: {e}")


if DEBUG:

    @app.before_server_start
    async def print_routes(app, loop):
        print("All registered routes:")
        for route in app.router.routes:
            print(f"{route} ")


# 插件状态上报API
@app.route("/api/plugin/status", methods=["POST"])
async def report_plugin_status(request):
    """接收插件状态上报

    请求体格式：
    {
        "plugin_id": "插件ID",
        "plugin_type": "插件类型",
        "status": "状态信息",
        "timestamp": 1646123456.789,
        "details": {...}  # 可选，详细状态信息
    }
    """
    try:
        data = request.json

        # 验证必须字段
        required_fields = ["plugin_id", "plugin_type", "status", "timestamp"]
        for field in required_fields:
            if field not in data:
                return json({"error": f"缺少必须字段: {field}"}, status=400)

        # 更新插件状态
        plugin_id = data["plugin_id"]
        plugin_status[plugin_id] = {
            "plugin_id": plugin_id,
            "plugin_type": data["plugin_type"],
            "status": data["status"],
            "timestamp": data["timestamp"],
            "last_updated": time.time(),
            "details": data.get("details", {}),
        }

        logger.debug(f"插件状态已更新: {plugin_id}, 状态: {data['status']}")
        return json({"success": True})
    except Exception as e:
        logger.error(f"处理插件状态上报时出错: {str(e)}")
        return json({"error": str(e)}, status=500)


# 插件数据上报API
@app.route("/api/plugin/data", methods=["POST"])
async def report_plugin_data(request):
    """接收插件数据上报

    请求体格式：
    {
        "plugin_id": "插件ID",
        "plugin_type": "插件类型",
        "data_type": "数据类型",
        "data": {...},  # 实际数据
        "timestamp": 1646123456.789,
        "metadata": {...}  # 可选，元数据信息
    }
    """
    try:
        data = request.json

        # 验证必须字段
        required_fields = ["plugin_id", "plugin_type", "data_type", "data", "timestamp"]
        for field in required_fields:
            if field not in data:
                return json({"error": f"缺少必须字段: {field}"}, status=400)

        # 存储插件数据
        plugin_id = data["plugin_id"]
        if plugin_id not in plugin_data:
            plugin_data[plugin_id] = []

        # 添加数据条目
        data_entry = {
            "plugin_id": plugin_id,
            "plugin_type": data["plugin_type"],
            "data_type": data["data_type"],
            "data": data["data"],
            "timestamp": data["timestamp"],
            "received_at": time.time(),
            "metadata": data.get("metadata", {}),
        }

        plugin_data[plugin_id].append(data_entry)

        # 限制数据条目数量，删除最旧的数据
        if len(plugin_data[plugin_id]) > MAX_DATA_ENTRIES_PER_PLUGIN:
            plugin_data[plugin_id] = plugin_data[plugin_id][
                -MAX_DATA_ENTRIES_PER_PLUGIN:
            ]

        logger.debug(
            f"插件数据已存储: {plugin_id}, 类型: {data['data_type']}, 当前条目数: {len(plugin_data[plugin_id])}"
        )
        return json({"success": True})
    except Exception as e:
        logger.error(f"处理插件数据上报时出错: {str(e)}")
        return json({"error": str(e)}, status=500)


# 获取所有插件状态API
@app.route("/api/plugin/status", methods=["GET"])
async def get_all_plugin_status(request):
    """获取所有插件的状态信息

    可选查询参数：
    - plugin_type: 按插件类型过滤
    - status: 按状态值过滤
    """
    try:
        # 获取查询参数
        plugin_type = request.args.get("plugin_type")
        status = request.args.get("status")

        # 过滤状态数据
        filtered_status = {}
        for pid, pstatus in plugin_status.items():
            # 应用过滤条件
            if plugin_type and pstatus.get("plugin_type") != plugin_type:
                continue
            if status and pstatus.get("status") != status:
                continue
            filtered_status[pid] = pstatus

        return json(
            {
                "timestamp": time.time(),
                "count": len(filtered_status),
                "plugins": filtered_status,
            }
        )
    except Exception as e:
        logger.error(f"获取插件状态时出错: {str(e)}")
        return json({"error": str(e)}, status=500)


# 获取单个插件状态API
@app.route("/api/plugin/status/<plugin_id>", methods=["GET"])
async def get_plugin_status(request, plugin_id):
    """获取指定插件的状态信息"""
    try:
        if plugin_id not in plugin_status:
            return json({"error": f"插件不存在: {plugin_id}"}, status=404)

        return json({"timestamp": time.time(), "plugin": plugin_status[plugin_id]})
    except Exception as e:
        logger.error(f"获取插件状态时出错: {str(e)}")
        return json({"error": str(e)}, status=500)


# 获取所有插件数据API
@app.route("/api/plugin/data", methods=["GET"])
async def get_all_plugin_data(request):
    """获取所有插件的数据信息

    可选查询参数：
    - plugin_id: 按插件ID过滤
    - plugin_type: 按插件类型过滤
    - data_type: 按数据类型过滤
    - limit: 限制返回的数据条目数（默认100）
    """
    try:
        # 获取查询参数
        plugin_id = request.args.get("plugin_id")
        plugin_type = request.args.get("plugin_type")
        data_type = request.args.get("data_type")
        limit = int(request.args.get("limit", 100))

        # 过滤数据
        filtered_data = {}
        for pid, data_entries in plugin_data.items():
            # 应用插件ID过滤
            if plugin_id and pid != plugin_id:
                continue

            # 过滤数据条目
            filtered_entries = []
            for entry in data_entries:
                # 应用过滤条件
                if plugin_type and entry.get("plugin_type") != plugin_type:
                    continue
                if data_type and entry.get("data_type") != data_type:
                    continue
                filtered_entries.append(entry)

            # 限制返回条目数
            if filtered_entries:
                filtered_data[pid] = filtered_entries[-limit:]

        return json(
            {
                "timestamp": time.time(),
                "count": sum(len(entries) for entries in filtered_data.values()),
                "plugins": filtered_data,
            }
        )
    except Exception as e:
        logger.error(f"获取插件数据时出错: {str(e)}")
        return json({"error": str(e)}, status=500)


# 获取单个插件数据API
@app.route("/api/plugin/data/<plugin_id>", methods=["GET"])
async def get_plugin_data(request, plugin_id):
    """获取指定插件的数据信息

    可选查询参数：
    - data_type: 按数据类型过滤
    - limit: 限制返回的数据条目数（默认100）
    """
    try:
        if plugin_id not in plugin_data:
            return json({"error": f"插件数据不存在: {plugin_id}"}, status=404)

        # 获取查询参数
        data_type = request.args.get("data_type")
        limit = int(request.args.get("limit", 100))

        # 过滤数据条目
        entries = plugin_data[plugin_id]
        if data_type:
            entries = [
                entry for entry in entries if entry.get("data_type") == data_type
            ]

        # 限制返回条目数
        entries = entries[-limit:]

        return json(
            {
                "timestamp": time.time(),
                "plugin_id": plugin_id,
                "count": len(entries),
                "data": entries,
            }
        )
    except Exception as e:
        logger.error(f"获取插件数据时出错: {str(e)}")
        return json({"error": str(e)}, status=500)


@app.before_server_stop
async def server_stop(app: Sanic, loop: AbstractEventLoop):
    logger.info("stop")
    app.shutdown_tasks(timeout=3)


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=app.config.server_port, workers=1, dev=DEBUG)
