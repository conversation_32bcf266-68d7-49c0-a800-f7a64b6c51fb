"""Mock Inbound 插件，用于测试数据监控功能。

这个插件会生成模拟数据，用于测试监控系统。
"""

import asyncio
import time
import random
from typing import Dict, Any
from loguru import logger

from beezer.plugins._base import InboundPlugin
from beezer.type_model import MockConfig


class MockInbound(InboundPlugin):
    """Mock 数据输入插件。"""

    plugin_protocol = "mock"

    def __init__(self, config: MockConfig):
        super().__init__(config)
        # self.config = MockConfig.model_validate(config)
        self._task = None
        self._data_counter = 0

    async def start(self, on_data):
        """启动数据采集。"""
        await super().start(on_data)
        await self.report_status("starting", {"message": "正在启动Mock数据生成器"})

        # 启动数据生成任务
        self._task = asyncio.create_task(self._generate_data())

        await self.report_status(
            "running",
            {
                "interval": self.config.interval,
                "data_type": self.config.data_type,
                "error_rate": self.config.error_rate,
            },
        )

        logger.info(f"Mock Inbound {self.config.id} 已启动")

    async def stop(self):
        """停止数据采集。"""
        await self.report_status("stopping", {"message": "正在停止Mock数据生成器"})

        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
            self._task = None

        await super().stop()
        await self.report_status("stopped", {"total_generated": self._data_counter})

        logger.info(f"Mock Inbound {self.config.id} 已停止")

    async def read(self, config: Dict[str, Any]) -> Any:
        """读取数据（同步调用）。"""
        return self._generate_mock_data()

    async def _generate_data(self):
        """持续生成数据的后台任务。"""
        while self._running:
            try:
                # 模拟错误
                if random.random() < self.config.error_rate:
                    await self.report_status(
                        "error",
                        {
                            "error_message": "模拟数据生成错误",
                            "error_count": random.randint(1, 5),
                        },
                    )
                    await asyncio.sleep(1)
                    await self.report_status("running", {"recovered": True})
                    continue

                # 生成数据
                data = self._generate_mock_data()
                await self.emit_data(data)

                self._data_counter += 1

                # 定期上报状态
                if self._data_counter % 10 == 0:
                    await self.report_status(
                        "running",
                        {
                            "generated_count": self._data_counter,
                            "last_data_time": time.strftime("%H:%M:%S"),
                            "memory_usage": f"{random.randint(20, 80)}MB",
                            "cpu_usage": f"{random.randint(5, 30)}%",
                        },
                    )

                await asyncio.sleep(self.config.interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Mock数据生成出错: {e}")
                await self.report_status(
                    "error",
                    {
                        "error_message": str(e),
                        "error_time": time.strftime("%H:%M:%S"),
                    },
                )
                await asyncio.sleep(5)  # 错误后等待5秒再重试

    def _generate_mock_data(self) -> Dict[str, Any]:
        """生成模拟数据。"""
        base_data = {
            "timestamp": time.time(),
            "id": self.config.id,
            "sequence": self._data_counter,
            "status": 1,  # 正常状态
        }

        if self.config.data_type == "sensor":
            # 传感器数据
            base_data.update(
                {
                    "sensors": {
                        "temperature": round(random.uniform(20.0, 80.0), 2),
                        "humidity": round(random.uniform(30.0, 90.0), 2),
                        "pressure": round(random.uniform(1000.0, 1100.0), 2),
                        "vibration": round(random.uniform(0.0, 5.0), 3),
                    },
                    "location": {
                        "x": round(random.uniform(-100.0, 100.0), 2),
                        "y": round(random.uniform(-100.0, 100.0), 2),
                        "z": round(random.uniform(0.0, 50.0), 2),
                    },
                }
            )
        elif self.config.data_type == "machine":
            # 机器数据
            base_data.update(
                {
                    "machine": {
                        "speed": random.randint(800, 2000),
                        "load": random.randint(10, 95),
                        "power": round(random.uniform(5.0, 50.0), 2),
                        "efficiency": round(random.uniform(0.7, 0.98), 3),
                    },
                    "production": {
                        "count": random.randint(0, 10),
                        "quality": random.choice(["good", "good", "good", "defect"]),
                        "cycle_time": round(random.uniform(30.0, 120.0), 1),
                    },
                }
            )
        elif self.config.data_type == "modbus":
            # 模拟Modbus数据
            base_data.update(
                {
                    "registers": [random.randint(0, 65535) for _ in range(10)],
                    "coils": [random.choice([True, False]) for _ in range(8)],
                    "device_id": 1,
                    "function_code": 3,
                }
            )
        else:
            # 通用数据
            base_data.update(
                {
                    "values": [round(random.uniform(0.0, 100.0), 2) for _ in range(5)],
                    "flags": [random.choice([True, False]) for _ in range(3)],
                    "message": f"Mock data #{self._data_counter}",
                }
            )

        return base_data
