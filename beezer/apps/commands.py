from typing import Dict, Optional, List, Any
from enum import Enum
from pydantic import BaseModel


class AppType(str, Enum):
    """支持的应用类型"""

    SYNTEC = "syntec"
    SYNTEC_V1 = "syntecv1"
    SYNTEC_V2 = "syntecv2"
    SYNTEC_V3 = "syntecv3"
    SYNTEC_V4 = "syntecv4"
    SYNTEC_V5 = "syntecv5"
    MITSUBISHI = "mitsubishi"
    FANUC = "fanuc"
    CUSTOM = "custom"


class DockerCopyConfig(BaseModel):
    """Docker构建时的文件拷贝配置"""

    source_path: str
    container_path: str


class SupervisorAppConfig(BaseModel):
    """Supervisor应用配置模型"""

    name: str
    type: AppType
    args: List[Any] = []
    kwargs: Dict[str, Any] = {}

    # Supervisor特有配置
    command: Optional[str] = None  # 完整命令，如果提供则优先使用
    exe_path: Optional[str] = None  # 可执行文件路径
    wine_prefix: Optional[str] = None  # Wine前缀路径

    # 进程管理配置
    autostart: bool = True
    autorestart: bool = True
    numprocs: int = 1
    priority: int = 999

    # 用户和目录配置
    user: Optional[str] = None
    directory: Optional[str] = None

    # 日志配置
    stdout_logfile: Optional[str] = None
    stderr_logfile: Optional[str] = None
    stdout_logfile_maxbytes: str = "50MB"
    stdout_logfile_backups: int = 10
    stderr_logfile_maxbytes: str = "50MB"
    stderr_logfile_backups: int = 10

    # 环境变量
    env: Dict[str, str] = {}

    # 向后兼容的字段
    interpreter: Optional[str] = None
    instances: int = 1  # 映射到numprocs
    max_memory_restart: str = "260M"
    watch: bool = False

    def model_copy(
        self, *, update: Dict[str, Any] | None = None, deep: bool = False
    ) -> "SupervisorAppConfig":
        """重写model_copy方法，确保args和kwargs被深拷贝"""
        copy = super().model_copy(update=update, deep=True)
        copy.args = self.args.copy()
        copy.kwargs = self.kwargs.copy()
        copy.env = self.env.copy()
        return copy

    def model_post_init(self, __context=None) -> None:
        """模型初始化后的处理"""
        # 向后兼容：将instances映射到numprocs
        if self.instances != 1:
            self.numprocs = self.instances

        # 设置默认日志文件路径（在name更新后重新设置）
        self._update_log_files()

    def _update_log_files(self):
        """更新日志文件路径"""
        if not self.stdout_logfile or "/var/log/supervisor/" in self.stdout_logfile:
            self.stdout_logfile = f"/var/log/supervisor/{self.name}.log"
        if not self.stderr_logfile or "/var/log/supervisor/" in self.stderr_logfile:
            self.stderr_logfile = f"/var/log/supervisor/{self.name}_error.log"

    def get_command(self) -> str:
        """根据类型和参数生成完整的启动命令"""
        # 如果直接提供了command，则优先使用
        if self.command:
            return self.command

        base_cmd = self.get_base_command()
        args_str = " ".join(str(arg) for arg in self.args)

        # 过滤掉不应该出现在命令行中的kwargs
        filtered_kwargs = {}
        for k, v in self.kwargs.items():
            if k not in ["exe_path", "wine_prefix", "command"]:
                filtered_kwargs[k] = v

        kwargs_str = " ".join(f"--{k}={v}" for k, v in filtered_kwargs.items())
        return f"{base_cmd} {args_str} {kwargs_str}".strip()

    def get_base_command(self) -> str:
        """获取基础命令"""
        if self.type in [
            AppType.SYNTEC,
            AppType.SYNTEC_V1,
            AppType.SYNTEC_V2,
            AppType.SYNTEC_V3,
            AppType.SYNTEC_V4,
            AppType.SYNTEC_V5,
        ]:
            exe_path = self.exe_path or self.kwargs.get(
                "exe_path", f"/app/{self.type.value}/syntec.exe"
            )
            # wine_prefix将在环境变量中设置，这里只返回wine命令
            return f"wine {exe_path}"
        elif self.type == AppType.MITSUBISHI:
            exe_path = self.exe_path or self.kwargs.get(
                "exe_path", "/app/mitsubishi/mitsubishi.exe"
            )
            # wine_prefix将在环境变量中设置，这里只返回wine命令
            return f"wine {exe_path}"
        elif self.type == AppType.FANUC:
            exe_path = self.exe_path or self.kwargs.get(
                "exe_path", "/app/fanuc_linker/fanuc_linker"
            )
            return f"{exe_path}"
        else:
            return self.command or self.kwargs.get("command", "")

    def to_supervisor_config(self) -> str:
        """转换为Supervisor配置格式 - 只生成单个程序的配置"""
        # 确保日志文件路径是最新的
        self._update_log_files()

        config_lines = [
            f"[program:{self.name}]",
            f"command={self.get_command()}",
            f"autostart={'true' if self.autostart else 'false'}",
            f"autorestart={'true' if self.autorestart else 'false'}",
            f"numprocs={self.numprocs}",
            f"priority={self.priority}",
        ]

        # 添加用户配置
        if self.user:
            config_lines.append(f"user={self.user}")

        # 添加工作目录配置
        if self.directory:
            config_lines.append(f"directory={self.directory}")

        # 添加日志配置
        config_lines.extend(
            [
                f"stdout_logfile={self.stdout_logfile}",
                f"stderr_logfile={self.stderr_logfile}",
                f"stdout_logfile_maxbytes={self.stdout_logfile_maxbytes}",
                f"stdout_logfile_backups={self.stdout_logfile_backups}",
                f"stderr_logfile_maxbytes={self.stderr_logfile_maxbytes}",
                f"stderr_logfile_backups={self.stderr_logfile_backups}",
            ]
        )

        # 处理环境变量
        env_vars = self.env.copy()

        # 为wine应用添加WINEPREFIX环境变量
        if self.type in [
            AppType.SYNTEC,
            AppType.SYNTEC_V1,
            AppType.SYNTEC_V2,
            AppType.SYNTEC_V3,
            AppType.SYNTEC_V4,
            AppType.SYNTEC_V5,
            AppType.MITSUBISHI,
        ]:
            wine_prefix = self.wine_prefix or self.kwargs.get(
                "wine_prefix", "/app/.wine"
            )
            env_vars["WINEPREFIX"] = wine_prefix

        if env_vars:
            env_str = ",".join(f"{k}={v}" for k, v in env_vars.items())
            config_lines.append(f"environment={env_str}")

        return "\n".join(config_lines) + "\n"

    def write_supervisor_config(self) -> str:
        """将配置写入到/etc/supervisor/conf.d/目录下"""
        import os

        config_content = self.to_supervisor_config()
        config_dir = "/etc/supervisor/conf.d"
        config_file = os.path.join(config_dir, f"{self.name}.conf")

        # 确保目录存在
        os.makedirs(config_dir, exist_ok=True)

        # 写入配置文件
        with open(config_file, "w") as f:
            f.write(config_content)

        return config_file

    # 保持向后兼容性
    def to_pm2_config(self) -> dict:
        """转换为PM2配置格式（保持向后兼容性）"""
        return {
            "name": self.name,
            "script": self.get_command(),
            "interpreter": self.interpreter,
            "instances": self.instances,
            "autorestart": self.autorestart,
            "max_memory_restart": self.max_memory_restart,
            "watch": self.watch,
            "env": self.env,
        }


# 为了向后兼容，保留PM2AppConfig作为SupervisorAppConfig的别名
PM2AppConfig = SupervisorAppConfig


class AppRegistry:
    """应用注册表"""

    # 系统默认应用配置
    DEFAULT_APPS = {
        AppType.SYNTEC: {
            "docker": DockerCopyConfig(
                source_path="bin/syntec/", container_path="/app/syntec"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntec",
                type=AppType.SYNTEC,
                exe_path="/app/syntec/syntec.exe",
                wine_prefix="/app/.wine",
                directory="/app",
            ),
        },
        AppType.SYNTEC_V1: {
            "docker": DockerCopyConfig(
                source_path="bin/syntecv1", container_path="/app/syntecv1"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntecv1",
                type=AppType.SYNTEC_V1,
                exe_path="/app/syntecv1/syntec.exe",
                wine_prefix="/app/.wine",
                directory="/app",
            ),
        },
        AppType.SYNTEC_V2: {
            "docker": DockerCopyConfig(
                source_path="bin/syntecv2", container_path="/app/syntecv2"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntecv2",
                type=AppType.SYNTEC_V2,
                exe_path="/app/syntecv2/syntec.exe",
                wine_prefix="/app/.wine",
                directory="/app",
            ),
        },
        AppType.SYNTEC_V3: {
            "docker": DockerCopyConfig(
                source_path="bin/syntecv3", container_path="/app/syntecv3"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntecv3",
                type=AppType.SYNTEC_V3,
                exe_path="/app/syntecv3/syntec.exe",
                wine_prefix="/app/.wine",
                directory="/app",
            ),
        },
        AppType.SYNTEC_V4: {
            "docker": DockerCopyConfig(
                source_path="bin/syntecv4", container_path="/app/syntecv4"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntecv4",
                type=AppType.SYNTEC_V4,
                exe_path="/app/syntecv4/syntec.exe",
                wine_prefix="/app/.wine",
                directory="/app",
            ),
        },
        AppType.SYNTEC_V5: {
            "docker": DockerCopyConfig(
                source_path="bin/syntecv5", container_path="/app/syntecv5"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntecv5",
                type=AppType.SYNTEC_V5,
                exe_path="/app/syntecv5/syntec.exe",
                wine_prefix="/app/.wine",
                directory="/app",
            ),
        },
        AppType.MITSUBISHI: {
            "docker": DockerCopyConfig(
                source_path="bin/mitsubishi/mitsubishi.exe",
                container_path="/app/mitsubishi/mitsubishi.exe",
            ),
            "supervisor": SupervisorAppConfig(
                name="mitsubishi",
                type=AppType.MITSUBISHI,
                exe_path="/app/mitsubishi/mitsubishi.exe",
                wine_prefix="/app/.wine",
                directory="/app",
            ),
        },
        AppType.FANUC: {
            "docker": DockerCopyConfig(
                source_path="bin/fanuc/fanuc_linker",
                container_path="/app/fanuc/fanuc_linker",
            ),
            "supervisor": SupervisorAppConfig(
                name="fanuc_linker",
                type=AppType.FANUC,
                exe_path="/app/fanuc/fanuc_linker",
                directory="/app",
                env={"LD_LIBRARY_PATH": "/app/fanuc"},
            ),
        },
    }

    def __init__(self):
        self.apps = self.DEFAULT_APPS.copy()

    def get_app_config(self, app_type: AppType) -> Optional[dict]:
        """获取应用配置

        返回配置的深拷贝，以避免多次获取时相互影响
        """
        config = self.apps.get(app_type)
        if config is None:
            return None

        result = {}
        for key, value in config.items():
            if isinstance(value, SupervisorAppConfig):
                # 对SupervisorAppConfig进行特殊处理，确保完全独立的副本
                new_value = SupervisorAppConfig(**value.model_dump())
                result[key] = new_value
            elif hasattr(value, "copy"):
                result[key] = value.copy()
            else:
                result[key] = value
        return result

    def get_docker_copy_configs(self) -> List[DockerCopyConfig]:
        """获取所有Docker拷贝配置"""
        return [app["docker"] for app in self.apps.values()]

    def get_supervisor_configs(self) -> List[SupervisorAppConfig]:
        """获取所有Supervisor配置"""
        return [app["supervisor"] for app in self.apps.values()]

    def write_all_supervisor_configs(self) -> list:
        """将所有应用的配置写入到/etc/supervisor/conf.d/目录下"""
        config_files = []
        for app_config in self.get_supervisor_configs():
            config_file = app_config.write_supervisor_config()
            config_files.append(config_file)
        return config_files

    # 保持向后兼容性
    def get_pm2_configs(self) -> List[dict]:
        """获取所有PM2配置（保持向后兼容性）"""
        return [app["supervisor"].to_pm2_config() for app in self.apps.values()]

    def generate_pm2_config(self) -> dict:
        """生成完整的PM2配置文件内容（保持向后兼容性）"""
        return {"apps": self.get_pm2_configs()}

    def register_custom_app(
        self,
        name: str,
        docker_config: DockerCopyConfig,
        supervisor_config: SupervisorAppConfig,
    ) -> None:
        """注册自定义应用"""
        self.apps[name] = {"docker": docker_config, "supervisor": supervisor_config}


# 创建全局注册表实例
registry = AppRegistry()
