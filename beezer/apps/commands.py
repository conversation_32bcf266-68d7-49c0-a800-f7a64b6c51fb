from typing import Dict, Optional, List, Any
from enum import Enum
from pydantic import BaseModel


class AppType(str, Enum):
    """支持的应用类型"""

    SYNTEC = "syntec"
    SYNTEC_V1 = "syntecv1"
    SYNTEC_V2 = "syntecv2"
    SYNTEC_V3 = "syntecv3"
    SYNTEC_V4 = "syntecv4"
    SYNTEC_V5 = "syntecv5"
    MITSUBISHI = "mitsubishi"
    FANUC = "fanuc"
    CUSTOM = "custom"


class DockerCopyConfig(BaseModel):
    """Docker构建时的文件拷贝配置"""

    source_path: str
    container_path: str


class SupervisorAppConfig(BaseModel):
    """Supervisor应用配置模型"""

    name: str
    type: AppType
    args: List[Any] = []
    kwargs: Dict[str, Any] = {}
    interpreter: Optional[str] = None
    instances: int = 1
    autorestart: bool = True
    max_memory_restart: str = "260M"
    watch: bool = False
    env: Dict[str, str] = {}

    def model_copy(
        self, *, update: Dict[str, Any] | None = None, deep: bool = False
    ) -> "SupervisorAppConfig":
        """重写model_copy方法，确保args和kwargs被深拷贝"""
        copy = super().model_copy(update=update, deep=True)
        copy.args = self.args.copy()
        copy.kwargs = self.kwargs.copy()
        return copy

    def get_command(self) -> str:
        """根据类型和参数生成完整的启动命令"""
        base_cmd = self.get_base_command()
        args_str = " ".join(str(arg) for arg in self.args)
        kwargs_str = " ".join(f"--{k}={v}" for k, v in self.kwargs.items())
        return f"{base_cmd} {args_str} {kwargs_str}".strip()

    def get_base_command(self) -> str:
        """获取基础命令"""
        if self.type in [
            AppType.SYNTEC,
            AppType.SYNTEC_V1,
            AppType.SYNTEC_V2,
            AppType.SYNTEC_V3,
            AppType.SYNTEC_V4,
            AppType.SYNTEC_V5,
        ]:
            exe_path = self.kwargs.pop("exe_path", f"/app/{self.type.value}/syntec.exe")
            wine_prefix = self.kwargs.pop("wine_prefix", "/app/.wine")
            return f"WINEPREFIX={wine_prefix} wine {exe_path}"
        elif self.type == AppType.MITSUBISHI:
            exe_path = self.kwargs.pop("exe_path", "/app/mitsubishi/mitsubishi.exe")
            wine_prefix = self.kwargs.pop("wine_prefix", "/app/.wine")
            return f"WINEPREFIX={wine_prefix} wine {exe_path}"
        elif self.type == AppType.FANUC:
            exe_path = self.kwargs.pop("exe_path", "/app/fanuc_linker/fanuc_linker")
            return f"{exe_path}"
        else:
            return self.kwargs.pop("command", "")

    def to_supervisor_config(self) -> str:
        """转换为Supervisor配置格式"""
        config_lines = [
            f"[program:{self.name}]",
            f"command={self.get_command()}",
            "autostart=true",
            f"autorestart={'true' if self.autorestart else 'false'}",
            f"numprocs={self.instances}",
            f"stdout_logfile=/var/log/supervisor/{self.name}.log",
            f"stderr_logfile=/var/log/supervisor/{self.name}_error.log",
            "stdout_logfile_maxbytes=50MB",
            "stdout_logfile_backups=10",
            "stderr_logfile_maxbytes=50MB",
            "stderr_logfile_backups=10",
        ]

        if self.env:
            env_str = ",".join(f"{k}={v}" for k, v in self.env.items())
            config_lines.append(f"environment={env_str}")

        return "\n".join(config_lines) + "\n"

    # 保持向后兼容性
    def to_pm2_config(self) -> dict:
        """转换为PM2配置格式（保持向后兼容性）"""
        return {
            "name": self.name,
            "script": self.get_command(),
            "interpreter": self.interpreter,
            "instances": self.instances,
            "autorestart": self.autorestart,
            "max_memory_restart": self.max_memory_restart,
            "watch": self.watch,
            "env": self.env,
        }


# 为了向后兼容，保留PM2AppConfig作为SupervisorAppConfig的别名
PM2AppConfig = SupervisorAppConfig


class AppRegistry:
    """应用注册表"""

    # 系统默认应用配置
    DEFAULT_APPS = {
        AppType.SYNTEC: {
            "docker": DockerCopyConfig(
                source_path="bin/syntec/", container_path="/app/syntec"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntec",
                type=AppType.SYNTEC,
                args=[],
                kwargs={
                    "wine_prefix": "/app/.wine",
                    "exe_path": "/app/syntec/syntec.exe",
                },
            ),
        },
        AppType.SYNTEC_V1: {
            "docker": DockerCopyConfig(
                source_path="bin/syntecv1", container_path="/app/syntecv1"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntecv1",
                type=AppType.SYNTEC_V1,
                args=[],
                kwargs={
                    "wine_prefix": "/app/.wine",
                    "exe_path": "/app/syntecv1/syntec.exe",
                },
            ),
        },
        AppType.SYNTEC_V2: {
            "docker": DockerCopyConfig(
                source_path="bin/syntecv2", container_path="/app/syntecv2"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntecv2",
                type=AppType.SYNTEC_V2,
                args=[],
                kwargs={
                    "wine_prefix": "/app/.wine",
                    "exe_path": "/app/syntecv2/syntec.exe",
                },
            ),
        },
        AppType.SYNTEC_V3: {
            "docker": DockerCopyConfig(
                source_path="bin/syntecv3", container_path="/app/syntecv3"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntecv3",
                type=AppType.SYNTEC_V3,
                args=[],
                kwargs={
                    "wine_prefix": "/app/.wine",
                    "exe_path": "/app/syntecv3/syntec.exe",
                },
            ),
        },
        AppType.SYNTEC_V4: {
            "docker": DockerCopyConfig(
                source_path="bin/syntecv4", container_path="/app/syntecv4"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntecv4",
                type=AppType.SYNTEC_V4,
                args=[],
                kwargs={
                    "wine_prefix": "/app/.wine",
                    "exe_path": "/app/syntecv4/syntec.exe",
                },
            ),
        },
        AppType.SYNTEC_V5: {
            "docker": DockerCopyConfig(
                source_path="bin/syntecv5", container_path="/app/syntecv5"
            ),
            "supervisor": SupervisorAppConfig(
                name="syntecv5",
                type=AppType.SYNTEC_V5,
                args=[],
                kwargs={
                    "wine_prefix": "/app/.wine",
                    "exe_path": "/app/syntecv5/syntec.exe",
                },
            ),
        },
        AppType.MITSUBISHI: {
            "docker": DockerCopyConfig(
                source_path="bin/mitsubishi/mitsubishi.exe",
                container_path="/app/mitsubishi/mitsubishi.exe",
            ),
            "supervisor": SupervisorAppConfig(
                name="mitsubishi",
                type=AppType.MITSUBISHI,
                args=[],
                kwargs={
                    "wine_prefix": "/app/.wine",
                    "exe_path": "/app/mitsubishi/mitsubishi.exe",
                },
            ),
        },
        AppType.FANUC: {
            "docker": DockerCopyConfig(
                source_path="bin/fanuc/fanuc_linker",
                container_path="/app/fanuc/fanuc_linker",
            ),
            "supervisor": SupervisorAppConfig(
                name="fanuc_linker",
                type=AppType.FANUC,
                args=[],
                kwargs={
                    "exe_path": "/app/fanuc/fanuc_linker",
                },
                env={"LD_LIBRARY_PATH": "/app/fanuc"},
            ),
        },
    }

    def __init__(self):
        self.apps = self.DEFAULT_APPS.copy()

    def get_app_config(self, app_type: AppType) -> Optional[dict]:
        """获取应用配置

        返回配置的深拷贝，以避免多次获取时相互影响
        """
        config = self.apps.get(app_type)
        if config is None:
            return None

        result = {}
        for key, value in config.items():
            if isinstance(value, SupervisorAppConfig):
                # 对SupervisorAppConfig进行特殊处理，确保完全独立的副本
                new_value = SupervisorAppConfig(**value.model_dump())
                result[key] = new_value
            elif hasattr(value, "copy"):
                result[key] = value.copy()
            else:
                result[key] = value
        return result

    def get_docker_copy_configs(self) -> List[DockerCopyConfig]:
        """获取所有Docker拷贝配置"""
        return [app["docker"] for app in self.apps.values()]

    def get_supervisor_configs(self) -> List[SupervisorAppConfig]:
        """获取所有Supervisor配置"""
        return [app["supervisor"] for app in self.apps.values()]

    def generate_supervisor_config(self) -> str:
        """生成完整的Supervisor配置文件内容"""
        config_content = "[supervisord]\n"
        config_content += "nodaemon=true\n"
        config_content += "logfile=/var/log/supervisor/supervisord.log\n"
        config_content += "pidfile=/var/run/supervisord.pid\n\n"

        config_content += "[unix_http_server]\n"
        config_content += "file=/var/run/supervisor.sock\n\n"

        config_content += "[supervisorctl]\n"
        config_content += "serverurl=unix:///var/run/supervisor.sock\n\n"

        config_content += "[rpcinterface:supervisor]\n"
        config_content += "supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface\n\n"

        for app_config in self.get_supervisor_configs():
            config_content += app_config.to_supervisor_config() + "\n"

        return config_content

    # 保持向后兼容性
    def get_pm2_configs(self) -> List[dict]:
        """获取所有PM2配置（保持向后兼容性）"""
        return [app["supervisor"].to_pm2_config() for app in self.apps.values()]

    def generate_pm2_config(self) -> dict:
        """生成完整的PM2配置文件内容（保持向后兼容性）"""
        return {"apps": self.get_pm2_configs()}

    def register_custom_app(
        self,
        name: str,
        docker_config: DockerCopyConfig,
        supervisor_config: SupervisorAppConfig,
    ) -> None:
        """注册自定义应用"""
        self.apps[name] = {"docker": docker_config, "supervisor": supervisor_config}


# 创建全局注册表实例
registry = AppRegistry()
