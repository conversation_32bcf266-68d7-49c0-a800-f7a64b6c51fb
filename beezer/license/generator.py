import os
import struct
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding as symmetric_padding
from cryptography.hazmat.backends import default_backend
import json
import base64
from datetime import datetime, timedelta
from beezer.license.machine_id import get_machine_id


class LicenseGenerator:
    """License生成器 - 服务端"""

    def __init__(self, private_key_path: str, public_key_path: str):
        if private_key_path and public_key_path:
            with open(private_key_path, "rb") as f:
                self.private_key = serialization.load_pem_private_key(
                    f.read(), password=None, backend=default_backend()
                )

            with open(public_key_path, "rb") as f:
                self.public_key = serialization.load_pem_public_key(
                    f.read(), backend=default_backend()
                )
        else:
            # 生成RSA密钥对
            self.private_key = rsa.generate_private_key(
                public_exponent=65537, key_size=2048, backend=default_backend()
            )
            self.public_key = self.private_key.public_key()

    def generate_keys(self, private_path, public_path):
        """生成并保存RSA密钥对"""
        with open(private_path, "wb") as f:
            f.write(
                self.private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption(),
                )
            )

        with open(public_path, "wb") as f:
            f.write(
                self.public_key.public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo,
                )
            )

    def generate_license(self, license_info: dict) -> bytes:
        """
        生成License
        结构：AesKey(16) + iv(16) + AesEnc(data).length(4) + AesEnc(data) + RsaSign(AesEnc(data))
        """
        # 生成16字节AES密钥
        aes_key = os.urandom(16)

        # 序列化license信息
        license_data = json.dumps(license_info).encode("utf-8")

        # 使用AES加密数据
        iv = os.urandom(16)
        cipher = Cipher(
            algorithms.AES(aes_key), modes.CBC(iv), backend=default_backend()
        )
        encryptor = cipher.encryptor()

        # PKCS7填充
        padder = symmetric_padding.PKCS7(128).padder()
        padded_data = padder.update(license_data) + padder.finalize()

        # 加密数据
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()

        # 计算加密数据长度
        encrypted_data_length = len(encrypted_data)

        # 对加密数据签名
        signature = self.private_key.sign(
            encrypted_data,
            padding.PSS(
                mgf=padding.MGF1(hashes.SHA256()),
                salt_length=padding.PSS.MAX_LENGTH,
            ),
            hashes.SHA256(),
        )

        # 组装最终的License
        license_bytes = (
            aes_key  # AES密钥(16字节)
            + iv  # IV(16字节)
            + struct.pack(">I", encrypted_data_length)  # 加密数据长度(4字节)
            + encrypted_data  # 加密数据
            + signature  # RSA签名
        )

        return base64.b64encode(license_bytes)


# 使用示例
def main():
    # 在授权服务器端
    generator = LicenseGenerator("private.pem", "public.pem")
    # generator.generate_keys("private.pem", "public.pem")
    # machine_id = get_machine_id()
    machine_id = "5c57245accb03185ebd1bd46c6ce9f64eab492af1744bdc1e695d37dfd23c0ce"
    print("machine_id", machine_id)
    # 生成license信息
    license_info = {
        "user": "beezer",
        "machine_id": machine_id,
        "expiry_date": (datetime.now() + timedelta(days=365 * 10)).isoformat(),
    }

    # 生成license
    license_str = generator.generate_license(license_info)
    print("Generated License:", license_str)

    # # 在客户端验证
    # verifier = LicenseVerifier("public.pem")

    # # 模拟当前环境信息
    # environment = {"user": "xxx", "machine_id": "MACHINE123"}

    # try:
    #     verified_info = verifier.verify_license(license_str, environment)
    #     print("License verification successful!")
    #     print("License info:", verified_info)
    # except Exception as e:
    #     print("License verification failed:", str(e))


if __name__ == "__main__":
    main()
