"""Beezer 的 Flet GUI 主应用。"""

import flet as ft
import asyncio
from loguru import logger
from beezer.gui.config_manager import ConfigManager
from beezer.gui.views.inbound_view import InboundView
from beezer.gui.views.monitor_view import MonitorView
from beezer.gui.views.apps_view import AppsView
from beezer.gui.bee_runtime import Bee<PERSON><PERSON><PERSON><PERSON>


def create_inbound_tab(config_manager, page, views=None):
    """创建 Inbound 标签页内容。"""
    # 使用新的 InboundView 类
    inbound_view = InboundView(config_manager, page)
    if views is not None:
        views["inbound"] = inbound_view
    return inbound_view.build()


def create_apps_tab(config_manager, page, views=None):
    """创建 Apps 标签页内容。"""
    # 使用新的 AppsView 类
    apps_view = AppsView(config_manager, page)
    if views is not None:
        views["apps"] = apps_view
    return apps_view.build()


def create_monitor_tab(page):
    """创建监控标签页内容。"""
    logger.info("开始创建监控标签页")
    monitor_view = MonitorView(page)
    logger.info("MonitorView创建完成")

    # 启动初始化任务
    async def init_monitor():
        logger.info("开始初始化监控视图")
        try:
            await monitor_view.did_mount()
            logger.info("监控视图初始化完成")
        except Exception as e:
            logger.error(f"监控视图初始化失败: {e}")
            import traceback

            logger.error(f"错误详情: {traceback.format_exc()}")

    page.run_task(init_monitor)

    content = monitor_view.build()
    logger.info("监控标签页创建完成")
    return content


def create_runtime_tab(page, config_manager):
    """创建运行时管理标签页内容。"""
    runtime = BeezerRuntime(config_manager.config_path)

    # 创建运行时管理界面
    status_text = ft.Text("已停止", size=16, weight=ft.FontWeight.BOLD)
    start_button = ft.ElevatedButton("启动", icon=ft.Icons.PLAY_ARROW)
    stop_button = ft.ElevatedButton("停止", icon=ft.Icons.STOP, disabled=True)
    restart_button = ft.ElevatedButton("重启", icon=ft.Icons.REFRESH, disabled=True)

    async def update_ui():
        """更新UI状态。"""
        status_info = runtime.get_status()
        status = status_info["status"]

        status_text.value = status
        start_button.disabled = status in ["starting", "running"]
        stop_button.disabled = status in ["stopped", "stopping"]
        restart_button.disabled = status in ["stopped", "stopping", "starting"]

        # 更新状态颜色
        color_map = {
            "running": ft.Colors.GREEN,
            "stopped": ft.Colors.GREY,
            "starting": ft.Colors.ORANGE,
            "stopping": ft.Colors.ORANGE,
            "error": ft.Colors.RED,
        }
        status_text.color = color_map.get(status, ft.Colors.BLACK)

        page.update()

    async def on_start(e):
        """启动按钮点击事件。"""
        await runtime.start()
        await update_ui()

    async def on_stop(e):
        """停止按钮点击事件。"""
        await runtime.stop()
        await update_ui()

    async def on_restart(e):
        """重启按钮点击事件。"""
        await runtime.restart()
        await update_ui()

    start_button.on_click = on_start
    stop_button.on_click = on_stop
    restart_button.on_click = on_restart

    # 添加状态变化回调
    def on_status_change(status, details):
        """状态变化回调。"""
        asyncio.create_task(update_ui())

    runtime.add_status_callback(on_status_change)

    return ft.Column(
        [
            ft.Text("Beezer 运行时管理", size=24, weight=ft.FontWeight.BOLD),
            ft.Divider(),
            ft.Row(
                [
                    ft.Text("服务状态:", size=16),
                    status_text,
                ]
            ),
            ft.Container(height=20),
            ft.Row(
                [start_button, stop_button, restart_button],
                spacing=10,
            ),
            ft.Container(height=20),
            ft.Text("配置文件:", size=14, weight=ft.FontWeight.BOLD),
            ft.Text(runtime.config_path, size=12, color=ft.Colors.GREY_600),
        ],
        spacing=10,
    )


def test_config_manager(e, config_manager):
    """测试配置管理器功能。"""
    try:
        logger.info("测试配置管理器...")

        # 检查配置是否已加载
        if config_manager.config is None:
            logger.warning("配置尚未加载")
            e.page.show_snack_bar(
                ft.SnackBar(content=ft.Text("配置尚未加载，请等待配置加载完成"))
            )
            return

        inbounds = config_manager.get_inbounds()
        outbounds = config_manager.get_outbounds()
        rules = config_manager.get_rules()
        flows = config_manager.get_flows()

        logger.info(
            f"当前配置统计: Inbound={len(inbounds)}, Outbound={len(outbounds)}, Rule={len(rules)}, Flow={len(flows)}"
        )

        # 显示前几个 Inbound 的详细信息
        if inbounds:
            first_inbound_id = list(inbounds.keys())[0]
            first_inbound = inbounds[first_inbound_id]
            logger.info(f"第一个 Inbound: {first_inbound_id} -> {first_inbound.type}")

        # 显示结果
        message = f"配置管理器测试成功！\\n"
        message += f"Inbound: {len(inbounds)} 个\\n"
        message += f"Outbound: {len(outbounds)} 个\\n"
        message += f"Rule: {len(rules)} 个\\n"
        message += f"Flow: {len(flows)} 个"

        e.page.show_snack_bar(ft.SnackBar(content=ft.Text(message)))
    except Exception as ex:
        logger.error(f"配置管理器测试失败: {ex}")
        import traceback

        logger.error(f"详细错误: {traceback.format_exc()}")
        e.page.show_snack_bar(ft.SnackBar(content=ft.Text(f"测试失败: {str(ex)}")))


def main(page: ft.Page):
    """Flet 应用主函数。

    Args:
        page: Flet 页面对象
    """
    # 设置页面属性
    page.title = "Beezer 配置器"
    page.theme_mode = ft.ThemeMode.SYSTEM
    page.padding = 20
    page.scroll = ft.ScrollMode.ADAPTIVE  # 启用页面滚动

    # 创建配置管理器
    config_manager = ConfigManager()

    # 保存视图引用，用于配置加载后刷新
    views = {}

    # 创建状态显示
    status_icon = ft.Icon(ft.Icons.CIRCLE, color="red", size=16)
    status_text = ft.Text("未运行", weight=ft.FontWeight.BOLD)

    # 异步加载配置
    async def load_config():
        try:
            logger.info("开始加载配置...")
            status_text.value = "正在加载配置..."
            status_icon.color = "orange"
            page.update()

            success = await config_manager.load_config()
            if success:
                inbounds = config_manager.get_inbounds()
                apps = config_manager.get_apps()
                logger.info(
                    f"配置加载成功，找到 {len(inbounds)} 个 Inbound, {len(apps)} 个 App"
                )
                status_text.value = (
                    f"配置已加载 ({len(inbounds)} 个 Inbound, {len(apps)} 个 App)"
                )
                status_icon.color = "blue"

                # 刷新所有视图
                for view_name, view in views.items():
                    if hasattr(view, "refresh"):
                        try:
                            view.refresh()
                            logger.info(f"已刷新 {view_name} 视图")
                        except Exception as ex:
                            logger.error(f"刷新 {view_name} 视图失败: {ex}")
            else:
                logger.error("配置加载失败")
                status_text.value = "配置加载失败"
                status_icon.color = "red"
        except Exception as e:
            logger.error(f"加载配置时发生错误: {e}")
            import traceback

            logger.error(f"详细错误: {traceback.format_exc()}")
            status_text.value = f"配置加载错误: {str(e)}"
            status_icon.color = "red"
        page.update()

    # 创建标题
    title = ft.Text("Beezer 配置器", size=30, weight=ft.FontWeight.BOLD)

    # 创建状态容器
    status_container = ft.Container(
        content=ft.Row([status_icon, status_text]),
        padding=10,
        border_radius=5,
        bgcolor="red50",
    )

    # 创建操作按钮
    def on_start_service(e):
        logger.info("启动服务按钮被点击 - 待实现")
        status_text.value = "启动中..."
        status_icon.color = "orange"
        page.update()

    def on_stop_service(e):
        logger.info("停止服务按钮被点击 - 待实现")
        status_text.value = "已停止"
        status_icon.color = "red"
        page.update()

    async def on_save_config(e):
        try:
            success = await config_manager.save_config()
            if success:
                logger.info("配置保存成功")
                status_text.value = "配置已保存"
                status_icon.color = "green"
            else:
                logger.error("配置保存失败")
                status_text.value = "保存失败"
                status_icon.color = "red"
        except Exception as ex:
            logger.error(f"保存配置时发生错误: {ex}")
            status_text.value = "保存错误"
            status_icon.color = "red"
        page.update()

    async def on_restart_service(e):
        """重启Supervisor服务按钮点击事件。"""
        try:
            logger.info("开始重启Supervisor服务...")
            status_text.value = "正在重启服务..."
            status_icon.color = "orange"
            page.update()

            # 执行Supervisor重启命令
            process = await asyncio.create_subprocess_shell(
                "supervisorctl restart all",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info("PM2服务重启成功")
                status_text.value = "服务重启成功"
                status_icon.color = "green"
                page.show_snack_bar(ft.SnackBar(content=ft.Text("PM2服务重启成功！")))
            else:
                error_msg = stderr.decode() if stderr else "未知错误"
                logger.error(f"PM2服务重启失败: {error_msg}")
                status_text.value = "服务重启失败"
                status_icon.color = "red"
                page.show_snack_bar(
                    ft.SnackBar(content=ft.Text(f"服务重启失败: {error_msg}"))
                )

        except Exception as ex:
            logger.error(f"重启服务时发生错误: {ex}")
            status_text.value = "重启错误"
            status_icon.color = "red"
            page.show_snack_bar(ft.SnackBar(content=ft.Text(f"重启失败: {str(ex)}")))
        page.update()

    action_buttons = ft.Row(
        [
            ft.ElevatedButton(
                text="启动服务",
                icon=ft.Icons.PLAY_ARROW_ROUNDED,
                on_click=on_start_service,
            ),
            ft.OutlinedButton(
                text="停止服务",
                icon=ft.Icons.STOP_ROUNDED,
                on_click=on_stop_service,
            ),
            ft.OutlinedButton(
                text="重启服务",
                icon=ft.Icons.RESTART_ALT_ROUNDED,
                on_click=on_restart_service,
            ),
            ft.OutlinedButton(
                text="保存配置",
                icon=ft.Icons.SAVE_ROUNDED,
                on_click=on_save_config,
            ),
        ]
    )

    # 创建标签页
    tabs = ft.Tabs(
        selected_index=0,
        animation_duration=300,
        tabs=[
            ft.Tab(
                text="Apps",
                icon=ft.Icons.APPS,
                content=create_apps_tab(config_manager, page, views),
            ),
            ft.Tab(
                text="Inbound",
                icon=ft.Icons.INPUT_ROUNDED,
                content=create_inbound_tab(config_manager, page, views),
            ),
            ft.Tab(
                text="Rule",
                icon=ft.Icons.RULE_FOLDER_ROUNDED,
                content=ft.Container(
                    content=ft.Text("Rule 配置区域 - 待实现"),
                    alignment=ft.alignment.center,
                    padding=20,
                    height=300,
                ),
            ),
            ft.Tab(
                text="Outbound",
                icon=ft.Icons.OUTPUT_ROUNDED,
                content=ft.Container(
                    content=ft.Text("Outbound 配置区域 - 待实现"),
                    alignment=ft.alignment.center,
                    padding=20,
                    height=300,
                ),
            ),
            ft.Tab(
                text="监控",
                icon=ft.Icons.MONITOR_ROUNDED,
                content=create_monitor_tab(page),
            ),
            ft.Tab(
                text="运行时",
                icon=ft.Icons.SETTINGS_APPLICATIONS,
                content=create_runtime_tab(page, config_manager),
            ),
        ],
        expand=1,
    )

    # 将所有组件添加到页面
    page.add(
        ft.Row([title, status_container], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
        ft.Divider(),
        action_buttons,
        ft.Divider(),
        tabs,
    )

    page.update()

    # 启动配置加载
    page.run_task(load_config)


def start_app(port: int = 8081):
    """启动 Flet 应用。

    Args:
        port: 应用端口号，默认为 8081
    """
    # 使用 web 模式启动应用
    ft.app(target=main, host="0.0.0.0", port=port, view=ft.AppView.WEB_BROWSER)
