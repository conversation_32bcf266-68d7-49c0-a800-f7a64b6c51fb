import asyncio
from typing import Optional
from beezer.apps.commands import SupervisorAppConfig


def read_process_config():
    try:
        with open("/app/supervisord.conf", "r") as f:
            return f.read()
    except FileNotFoundError:
        # 如果supervisor配置文件不存在，返回基本配置
        return """[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[unix_http_server]
file=/var/run/supervisor.sock

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

"""


PROCESS_CONFIG = read_process_config()


def write_process_config(config):
    with open("/app/supervisord.conf", "w") as f:
        f.write(config)


async def gen_supervisor_config(
    program_name: str,
    program_command: str,
    supervisor_config: Optional[SupervisorAppConfig] = None,
):
    """生成supervisor配置"""
    base_config = """[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[unix_http_server]
file=/var/run/supervisor.sock

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

"""

    if supervisor_config:
        # 使用提供的supervisor配置
        program_config = supervisor_config.to_supervisor_config()
    else:
        # 使用默认配置
        program_config = f"""[program:{program_name}]
command={program_command}
autostart=true
autorestart=true
numprocs=1
stdout_logfile=/var/log/supervisor/{program_name}.log
stderr_logfile=/var/log/supervisor/{program_name}_error.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10

"""

    return base_config + program_config


async def restart_process(program_name: str):
    """重启supervisor管理的进程"""
    await asyncio.create_subprocess_shell(
        f"supervisorctl restart {program_name}",
    )


async def start_process(
    program_name: str,
    program_command: str,
    supervisor_config: Optional[SupervisorAppConfig] = None,
):
    """启动supervisor管理的进程"""
    global PROCESS_CONFIG

    # 生成新的配置
    new_config = await gen_supervisor_config(
        program_name, program_command, supervisor_config
    )

    # 检查是否需要更新配置文件
    if new_config != PROCESS_CONFIG:
        PROCESS_CONFIG = new_config
        write_process_config(PROCESS_CONFIG)

        # 重新加载supervisor配置
        await asyncio.create_subprocess_shell("supervisorctl reread")
        await asyncio.create_subprocess_shell("supervisorctl update")

    # 启动或重启进程
    await asyncio.create_subprocess_shell(f"supervisorctl start {program_name}")


# 保持向后兼容性的别名
gen_process_config = gen_supervisor_config
