import asyncio
import os
from typing import Optional
from beezer.apps.commands import SupervisorAppConfig


def write_supervisor_app_config(program_name: str, config_content: str) -> str:
    """将单个应用配置写入到/etc/supervisor/conf.d/目录下"""
    config_dir = "/etc/supervisor/conf.d"
    config_file = os.path.join(config_dir, f"{program_name}.conf")

    # 确保目录存在
    os.makedirs(config_dir, exist_ok=True)

    # 写入配置文件
    with open(config_file, "w") as f:
        f.write(config_content)

    return config_file


async def gen_supervisor_config(
    program_name: str,
    program_command: str,
    supervisor_config: Optional[SupervisorAppConfig] = None,
):
    """生成单个应用的supervisor配置"""
    if supervisor_config:
        # 使用提供的supervisor配置
        program_config = supervisor_config.to_supervisor_config()
    else:
        # 使用默认配置
        program_config = f"""[program:{program_name}]
command={program_command}
autostart=true
autorestart=true
numprocs=1
stdout_logfile=/var/log/supervisor/{program_name}.log
stderr_logfile=/var/log/supervisor/{program_name}_error.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10

"""

    return program_config


async def restart_process(program_name: str):
    """重启supervisor管理的进程"""
    await asyncio.create_subprocess_shell(
        f"supervisorctl restart {program_name}",
    )


async def start_process(
    program_name: str,
    program_command: str,
    supervisor_config: Optional[SupervisorAppConfig] = None,
):
    """启动supervisor管理的进程"""
    # 生成新的配置
    config_content = await gen_supervisor_config(
        program_name, program_command, supervisor_config
    )

    # 将配置写入到/etc/supervisor/conf.d/目录下
    config_file = write_supervisor_app_config(program_name, config_content)

    # 重新加载supervisor配置
    await asyncio.create_subprocess_shell("supervisorctl reread")
    await asyncio.create_subprocess_shell("supervisorctl update")

    # 启动或重启进程
    await asyncio.create_subprocess_shell(f"supervisorctl start {program_name}")


# 保持向后兼容性的别名
gen_process_config = gen_supervisor_config
