import asyncio
import yaml
from typing import Optional, Union
from beezer.apps.commands import PM2AppConfig


def read_process_config():
    with open("/app/process.yml", "r") as f:
        return yaml.safe_load(f)


PROCESS_CONFIG = read_process_config()


def write_process_config(config):
    with open("/app/process.yml", "w") as f:
        yaml.dump(config, f)


async def gen_process_config(
    program_name: str, program_command: str, pm2_config: Optional[PM2AppConfig] = None
):
    process_config = PROCESS_CONFIG
    for app_config in process_config["apps"]:
        if app_config["name"] == program_name:
            app_config["script"] = program_command
            if pm2_config:
                # 更新PM2配置
                config = pm2_config.to_pm2_config()
                config["name"] = program_name
                app_config.update(config)
            return process_config
    else:
        if pm2_config:
            # 使用PM2AppConfig的配置
            config = pm2_config.to_pm2_config()
            config["name"] = program_name
            config["script"] = program_command
        else:
            # 使用默认配置
            config = {
                "name": program_name,
                "script": program_command,
                "args": "",
                "instances": 1,
                "autorestart": True,
                "watch": False,
                "max_memory_restart": "300M",
                "interpreter": None,
            }

        process_config["apps"].append(config)
        return process_config


async def restart_process(program_name: str):
    await asyncio.create_subprocess_shell(
        f"pm2 startOrReload /app/process.yml --only {program_name}",
    )


async def start_process(
    program_name: str, program_command: str, pm2_config: Optional[PM2AppConfig] = None
):
    global PROCESS_CONFIG
    PROCESS_CONFIG = await gen_process_config(program_name, program_command, pm2_config)
    write_process_config(PROCESS_CONFIG)
    # await restart_process(program_name)
