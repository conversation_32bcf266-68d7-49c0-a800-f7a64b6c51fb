#!/usr/bin/env python3
"""
测试PM2到Supervisor迁移的脚本
"""


def test_imports():
    """测试导入是否正常"""
    try:
        from beezer.apps.commands import SupervisorAppConfig, registry, AppType

        print("✓ 导入SupervisorAppConfig成功")

        from beezer.process import start_process, gen_supervisor_config

        print("✓ 导入process模块成功")

        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False


def test_supervisor_config_generation():
    """测试Supervisor配置生成"""
    try:
        from beezer.apps.commands import SupervisorAppConfig, AppType

        # 创建一个测试配置
        config = SupervisorAppConfig(
            name="test_app",
            type=AppType.CUSTOM,
            args=["--test"],
            kwargs={"command": "/bin/echo hello"},
        )

        # 生成supervisor配置
        supervisor_config = config.to_supervisor_config()
        print("✓ Supervisor配置生成成功:")
        print(supervisor_config)

        return True
    except Exception as e:
        print(f"✗ Supervisor配置生成失败: {e}")
        return False


def test_registry():
    """测试应用注册表"""
    try:
        from beezer.apps.commands import registry, AppType

        # 测试获取应用配置
        syntec_config = registry.get_app_config(AppType.SYNTEC)
        if syntec_config and "supervisor" in syntec_config:
            print("✓ 应用注册表配置正确")

            # 测试单个应用配置生成
            supervisor_app = syntec_config["supervisor"]
            app_config = supervisor_app.to_supervisor_config()
            if "[program:" in app_config and "command=" in app_config:
                print("✓ 单个应用Supervisor配置生成成功")
                return True
            else:
                print("✗ 单个应用Supervisor配置格式不正确")
                return False
        else:
            print("✗ 应用注册表配置不正确")
            return False
    except Exception as e:
        print(f"✗ 应用注册表测试失败: {e}")
        return False


def test_config_file_writing():
    """测试配置文件写入功能"""
    try:
        from beezer.apps.commands import SupervisorAppConfig, AppType

        # 创建测试配置
        config = SupervisorAppConfig(
            name="test_write",
            type=AppType.CUSTOM,
            kwargs={"command": "/bin/echo test"},
        )

        # 测试配置内容生成
        config_content = config.to_supervisor_config()
        if (
            "[program:test_write]" in config_content
            and "command=/bin/echo test" in config_content
        ):
            print("✓ 配置文件内容生成正常")
            return True
        else:
            print("✗ 配置文件内容不正确")
            print(f"生成的内容: {config_content}")
            return False

    except Exception as e:
        print(f"✗ 配置文件写入测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试PM2到Supervisor迁移...")
    print("=" * 50)

    tests = [
        ("导入测试", test_imports),
        ("Supervisor配置生成测试", test_supervisor_config_generation),
        ("应用注册表测试", test_registry),
        ("配置文件写入测试", test_config_file_writing),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"测试失败: {test_name}")

    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")

    if passed == total:
        print("✓ 所有测试通过！PM2到Supervisor迁移成功。")
        return True
    else:
        print("✗ 部分测试失败，请检查代码。")
        return False


if __name__ == "__main__":
    main()
