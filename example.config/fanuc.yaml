version: 1

inbounds:
  1:
    id: '*************'
    ip: "*************"
    type: http_client
    requests:
    - method: GET
      name: current
      url: http://localhost:8205/?func=current
    

rules:
  - name: status
    description: "status"
    rules:
    - source: "$"
      source_name: "current"
      target: "$"
      type: "reference"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]


outbounds:
  custom_ioteq:
    config:
      api_host: http://*************:9890/eq/eqEquipmentAccTabs/
      odbc_url: mssql+pyodbc://sa:123456@*************:1433/test?driver=ODBC+Driver+17+for+SQL+Server
    type: custom_ioteq
    
flows:
# inbound只有一种输出, outbound只有一种输入
  - inbounds: [1] # == ['gt01']
    name: flow01
    rules:
      - name: "status"
        action: "IOTEqStatues"
    outbound: 'custom_ioteq'

apps:
  # - name: "fanuc"
  #   type: "fanuc"
  #   args: ["*************", "http://localhost:3333"]
  - name: "mitsubishi"
    type: "mitsubishi"
    args: ["*************", "http://localhost:3334"]

# inbound有多种输出，可能需要多个输出结合到一个输入
  # - inbounds: [2,3]
  #   outbound: 'xinheyun'
  #   rules:
  #     - name: "rule01"
  #       onputs: ["sysinfo", "current"]
  #       input: "status"
        