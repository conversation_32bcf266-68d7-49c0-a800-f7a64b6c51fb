version: 1
inbound_templates:
  xisuji: &id001
    type: modbus
    port: 502
    interval: 2
    points:
      - address: 41824
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "左工位状态"
      - address: 41834
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右工位状态"
      - address: 41908
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "左工位加热时间"
      - address: 41958
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右工位加热时间"
      - address: 41878
        count: 2
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "产量"
      - address: 50211
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16' # Bit access may require specific handling
        byte_order: 'big'
        name: "加热状态"
      - address: 501
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16' # Bit access may require specific handling
        byte_order: 'big'
        name: "真空泵状态"
      - address: 40311
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "左参数-生产周期"
      - address: 41908
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "左参数-加热时间1"
      - address: 41892
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "左参数-吹气时间1"
      - address: 41896
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "左参数-冷风时间"
      - address: 41898
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "左参数-真空时间1"
      - address: 41899
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "左参数-真空时间2"
      - address: 41901
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "左参数-脱模吹气时间1"
      - address: 41902
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "左参数-脱模吹气时间2"
      - address: 411
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右参数-生产周期"
      - address: 41958
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右参数-加热时间"
      - address: 41956
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右参数-模具冷却时间"
      - address: 41941
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右参数-吹气时间1"
      - address: 41943
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右参数-吹气时间2"
      - address: 41945
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右参数-冷风时间"
      - address: 41947
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右参数-真空时间1"
      - address: 41948
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右参数-真空时间2"
      - address: 41950
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右参数-脱模吹气时间1"
      - address: 41951
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "右参数-脱模吹气时间2"
      - address: 18460
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16' # Bit access may require specific handling
        byte_order: 'big'
        name: "alarm"
  choubanji1: &id002
    type: modbus
    port: 502
    interval: 2
    slave: 2
    points:
      - address: 50009
        count: 1
        addr_type: 'holding_register'  # M points and bit types often map to coils
        value_type: 'uint16'
        byte_order: 'big'
        name: "主机螺杆电机状态"
      - address: 10
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "主机螺杆熔体泵"
      - address: 1001
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "主机螺杆速度"
      - address: 1003
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "主机螺杆负载"
      - address: 1005
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "主机熔体泵速度"
      - address: 1007
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "主机熔体泵负载"
      - address: 1011
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'little'
        name: "主机换网器压力"
      - address: 11
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "辅机螺杆电机状态"
      - address: 12
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "辅机熔体泵"
      - address: 1051
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "辅机螺杆速度"
      - address: 1053
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "辅机螺杆负载"
      - address: 1055
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "辅机熔体泵速度"
      - address: 1057
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "辅机熔体泵负载"
      - address: 1061
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "辅机换网器压力"
      - address: 7
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "液压站状态"
      - address: 8
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "真空泵状态"
      - address: 501
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "主机螺杆负载率报警"
      - address: 502
        count: 2  # Address range is 502-503
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'little'
        name: "主机熔体泵负载率报警"
      - address: 509
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "主机换网器报警压力"
      - address: 551
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "辅机螺杆负载率报警"
      - address: 553
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "辅机熔体泵负载率报警"
      - address: 559
        count: 2
        addr_type: 'holding_register'
        value_type: 'float'
        byte_order: 'big'
        name: "辅机换网器报警压力"
      - address: 6
        count: 1
        addr_type: 'holding_register'
        value_type: 'uint16'
        byte_order: 'big'
        name: "alarm"
  choubanji2: &id003
    type: modbus
    port: 502
    interval: 2
    slave: 2
    points:
      - address: 1501
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'float'  # 默认
        byte_order: 'big'  # 默认
        name: "上辊速度"
      - address: 1503
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'float'  # 默认
        byte_order: 'big'  # 默认
        name: "中辊速度"
      - address: 1505
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'float'  # 默认
        byte_order: 'big'  # 默认
        name: "下辊速度"
      - address: 1507
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'float'  # 默认
        byte_order: 'big'  # 默认
        name: "牵引速度"
      - address: 1511
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'float'  # 默认
        byte_order: 'big'  # 默认
        name: "倍率"
      - address: 21
        count: 1
        addr_type: 'holding_register'  # 默认
        value_type: 'uint16'  # 默认
        byte_order: 'big'  # 默认
        name: "1号油泵状态"
      - address: 22
        count: 1
        addr_type: 'holding_register'  # 默认
        value_type: 'uint16'  # 默认
        byte_order: 'big'  # 默认
        name: "2号油泵状态"
      - address: 23
        count: 1
        addr_type: 'holding_register'  # 默认
        value_type: 'uint16'  # 默认
        byte_order: 'big'  # 默认
        name: "3号油泵状态"
      - address: 24
        count: 1
        addr_type: 'holding_register'  # 默认
        value_type: 'uint16'  # 默认
        byte_order: 'big'  # 默认
        name: "4号油泵状态"
      - address: 25
        count: 1
        addr_type: 'holding_register'  # 默认
        value_type: 'uint16'  # 默认
        byte_order: 'big'  # 默认
        name: "增压泵状态"
      - address: 68
        count: 1
        addr_type: 'holding_register'  # 默认
        value_type: 'uint16'  # 默认
        byte_order: 'big'  # 默认
        name: "alarm"
  choubanji3: &id004
    type: modbus
    port: 502
    interval: 2
    slave: 2
    points:
      - address: 1403
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'uint32'  # 默认
        byte_order: 'big'  # 默认
        name: "当前产量"
      - address: 1461
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'uint32'  # 默认
        byte_order: 'big'  # 默认
        name: "当前长度"
      - address: 1401
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'uint32'  # 默认
        byte_order: 'big'  # 默认
        name: "累计产量"
      - address: 1020
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'float'  # 默认
        byte_order: 'big'  # 默认
        name: "长度设定"
      - address: 1003
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'float'  # 默认
        byte_order: 'big'  # 默认
        name: "提前切刀量"
      - address: 381
        count: 2
        addr_type: 'holding_register'  # 默认
        value_type: 'float'  # 默认
        byte_order: 'big'  # 默认
        name: "实际提前量"
      - address: 1007
        count: 1
        addr_type: 'holding_register'  # 默认
        value_type: 'uint16'  # 默认
        byte_order: 'big'  # 默认
        name: "压板延时"
      - address: 89
        count: 1
        addr_type: 'holding_register'  # 默认
        value_type: 'uint16'  # 默认
        byte_order: 'big'  # 默认
        name: "当前模式"
      - address: 5
        count: 1
        addr_type: 'holding_register'  # 默认
        value_type: 'uint16'  # 默认
        byte_order: 'big'  # 默认
        name: "alarm"

inbounds:
  "************":
    <<: *id001
    "id": "************"
    "ip": "************"
  "************":
    <<: *id002
    "id": "************"
    "ip": "************"
  "************":
    <<: *id003
    "id": "************"
    "ip": "************"
  "************":
    <<: *id004
    "id": "************"
    "ip": "************"
  "************":
    "id": "************"
    "ip": "************"
    type: http_client
    interval: 4
    requests:
    - method: GET
      name: current
      url: http://localhost:8214/?func=current
rules:
  - name: status
    rules:
    - source:
        source: "0 if var1 == -1 else 1 if var1 == 0 else 2"
        placeholder:
          var1: "$.alarm"
      target: "$.State"
      type: "expr"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]
  - name: alarm
    rules:
    - source:
        source: "1 if var1 == 1 else 0"
        placeholder:
          var1: "$.alarm"
      target: "$.State"
      type: "expr"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]
  - name: params
    rules:
      - source: "$"
        target: "$"
        type: "reference"
      - source: "$.id"
        target: "$.id"
        type: "reference"
      - source:
          source: "TIMESTAMP_TO_STR(var1)"
          placeholder:
            var1: "$.timestamp"
        target: "$.CreateDate"
        type: "expr"
      - source:
          source: "UUID1()"
        target: "$.Uuid"
        type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]
  
  - name: syntec_status
    description: "syntec_status"
    rules:
    - source:
        source: "2 if var1 != -1 else 0"
        placeholder:
          var1: "$.status"
      source_name: "current"
      target: "$.State"
      type: "expr"
    - source:
        source: "2 if var1 and var1 != 1024 else 0"
        placeholder:
          var1: "$.data.result"
      source_name: "current"
      target: "$.State"
      type: "expr"
    - source:
        source: "$.data.path[0].status"
        mapping:
          ACTIVE: 1
          STOPPED: 2
          STOP: 2
        default: 0
      source_name: "current"
      target: "$.State"
      type: "map"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]

  - name: syntec_count
    description: "syntec_count"
    rules:
    - source: "$.data.part_count"
      source_name: "current"
      target: "$.Count"
      type: "reference"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]

  - name: syntec_alarm
    description: "syntec_alarm"
    rules:
    - source:
        source: "1 if (var1 and len(var1)) or var2 == 'TRIGGERED' else 0"
        placeholder:
          var1: "$.data.alarm"
          var2: "$.data.emg"
      source_name: "current"
      target: "$.State"
      type: "expr"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]

  - name: syntec_current
    description: "current"
    rules:
    - source: "$.data.path[0].main_prog"
      source_name: "current"
      target: "$.main_prog"
      type: "reference"
    - source: "$.data.path[0].cur_prog"
      source_name: "current"
      target: "$.cur_prog"
      type: "reference"

    - source: "$.data.path[0].cur_seq"
      source_name: "current"
      target: "$.cur_seq"
      type: "reference"
    - source: "$.data.path[0].ov_feed"
      source_name: "current"
      target: "$.ov_feed"
      type: "reference"
    - source: "$.data.path[0].act_feed"
      source_name: "current"
      target: "$.act_feed"
      type: "reference"
    - source: "$.data.path[0].spindles"
      source_name: "current"
      target: "$.spindles"
      type: "reference"
    - source: "$.data.path[0].axes"
      source_name: "current"
      target: "$.axes"
      type: "reference"

    - source: "$.data.emg"
      source_name: "current"
      target: "$.emg"
      type: "reference"
    - source: "$.data.mode"
      source_name: "current"
      target: "$.mode"
      type: "reference"
    - source: "$.data.alarm"
      source_name: "current"
      target: "$.alarm"
      type: "reference"
    - source: "$.id"
      target: "$.id"
      type: "reference"
    - source:
        source: "TIMESTAMP_TO_STR(var1)"
        placeholder:
          var1: "$.timestamp"
      target: "$.CreateDate"
      type: "expr"
    - source:
        source: "UUID1()"
      target: "$.Uuid"
      type: "expr"
    trigger:
      mode: "change"
      type: "any"
      exclude: ["$.CreateDate", "$.Uuid"]


flows:
  - name: flow1
    inbounds:
      - "************"
      - "************"
      - "************"
      - "************"
    rules:
      - name: "status"
        action: "IOTEqStatues"
      - name: "alarm"
        action: "IOTEqWarning"
      - name: "params"
        action: "IotParametersGet"
    outbound: custom_ioteq
  - name: flow2
    inbounds:
      - "************"
    outbound: custom_ioteq
    rules:
      - name: "syntec_status"
        action: "IOTEqStatues"
      - name: "syntec_alarm"
        action: "IOTEqWarning"
      - name: "syntec_count"
        action: "IOTEqProduceCount"
      - name: "syntec_current"
        action: "IOTEqMachiningParams"

outbounds:
  custom_ioteq:
    id: aaa
    api_host: http://mes.hrxccl.com:9600/eq/eqEquipmentAccTabs/
    odbc_url: ""
    type: custom_ioteq

apps:
  - args:
    - ************
    - http://localhost:8214/
    name: syntec_214
    type: syntecv3