[tool.poetry]
name = "beezer"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.scripts]
beezer-gui = "beezer.gui.__main__:main"

[tool.poetry.dependencies]
python = "^3.12"
nuitka = "^2.4.8"
sanic = "^24.6.0"
pluggy = "^1.5.0"
pymodbus = "^3.8.2"
pydantic = "^2.9.2"
aiohttp = "^3.10.9"
loguru = "^0.7.2"
anyio = "^4.6.0"
pyyaml = "^6.0.2"
jsonpath-ng = "^1.7.0"
cryptography = "^43.0.3"
pyarmor = "^9.0.3"
jwt = "^1.3.1"
orjson = "^3.10.10"
pyodbc = "^5.2.0"
sqlalchemy = "^2.0.36"
scons = "^4.8.1"
bcrypt = "^4.0.1"
pyjwt = "^2.8.0"
sanic-jwt = "^1.8.0"
pandas = "^2.2.0"
openpyxl = "^3.1.2"
ruamel-yaml = "^0.18.10"
cffi = "^1.17.1"
tomli = "^2.2.1"
tomli-w = "^1.2.0"
flet = "^0.28.3"
flask = "^3.1.1"
websockets = "^13.1"
paho-mqtt = "^2.1.0"

[tool.poetry.group.dev.dependencies]
ruff = "^0.6.5"
pytest-asyncio = "^1.0.0"
sanic-testing = "^24.6.0"
pytest = "^8.3.5"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
