#!/usr/bin/env python3
"""
验证MQTT WebSocket Outbound插件是否正确实现的脚本
"""

import sys
import traceback


def test_import():
    """测试导入"""
    try:
        print("1. 测试导入type_model...")
        from beezer.type_model import MqttWebSocketOutboundConfig, MqttTopicConfig, OutboundTypes
        print("   ✅ type_model导入成功")
        
        print("2. 测试导入插件...")
        from beezer.plugins.outbounds.mqtt_websocket_outbound import MqttWebSocketOutbound
        print("   ✅ 插件导入成功")
        
        return True
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        traceback.print_exc()
        return False


def test_config_validation():
    """测试配置验证"""
    try:
        print("3. 测试配置验证...")
        from beezer.type_model import MqttWebSocketOutboundConfig
        
        # 测试基本配置
        config_data = {
            "type": "mqtt_websocket",
            "id": "test_mqtt",
            "broker_url": "ws://localhost:9001/mqtt",
            "topics": {
                "test_action": {
                    "topic": "test/topic",
                    "qos": 1
                }
            }
        }
        
        config = MqttWebSocketOutboundConfig.model_validate(config_data)
        print(f"   ✅ 配置验证成功: {config.id}")
        
        return True
    except Exception as e:
        print(f"   ❌ 配置验证失败: {e}")
        traceback.print_exc()
        return False


def test_plugin_creation():
    """测试插件创建"""
    try:
        print("4. 测试插件创建...")
        from beezer.plugins.outbounds.mqtt_websocket_outbound import MqttWebSocketOutbound
        
        config_data = {
            "type": "mqtt_websocket",
            "id": "test_mqtt",
            "broker_url": "ws://localhost:9001/mqtt",
            "topics": {
                "sensor_data": {
                    "topic": "sensors/data",
                    "qos": 1
                },
                "alarm_data": {
                    "topic": "alarms/critical",
                    "qos": 2
                }
            }
        }
        
        plugin = MqttWebSocketOutbound(config_data)
        print(f"   ✅ 插件创建成功: {plugin.config.id}")
        
        # 检查actions是否正确注册
        expected_actions = ["sensor_data", "alarm_data", "publish", "send"]
        for action in expected_actions:
            if action in plugin.actions:
                print(f"   ✅ Action '{action}' 已注册")
            else:
                print(f"   ❌ Action '{action}' 未注册")
                return False
        
        return True
    except Exception as e:
        print(f"   ❌ 插件创建失败: {e}")
        traceback.print_exc()
        return False


def test_plugin_registry():
    """测试插件注册"""
    try:
        print("5. 测试插件注册...")
        from beezer.plugins import PLUGIN_MODULES
        
        if "mqtt_websocket" in PLUGIN_MODULES:
            print("   ✅ 插件已在PLUGIN_MODULES中注册")
            return True
        else:
            print("   ❌ 插件未在PLUGIN_MODULES中注册")
            return False
    except Exception as e:
        print(f"   ❌ 插件注册检查失败: {e}")
        traceback.print_exc()
        return False


def test_outbound_types():
    """测试OutboundTypes枚举"""
    try:
        print("6. 测试OutboundTypes枚举...")
        from beezer.type_model import OutboundTypes
        
        if hasattr(OutboundTypes, 'MqttWebSocket'):
            print(f"   ✅ MqttWebSocket类型已添加: {OutboundTypes.MqttWebSocket}")
            return True
        else:
            print("   ❌ MqttWebSocket类型未添加到OutboundTypes")
            return False
    except Exception as e:
        print(f"   ❌ OutboundTypes检查失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("MQTT WebSocket Outbound 插件验证")
    print("=" * 50)
    
    tests = [
        test_import,
        test_config_validation,
        test_plugin_creation,
        test_plugin_registry,
        test_outbound_types,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！插件实现正确。")
        return 0
    else:
        print("❌ 部分测试失败，请检查实现。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
