#!/usr/bin/env python3
"""
验证MQTT WebSocket Outbound插件的改进功能

验证项目：
1. max_reconnect_attempts = 0 表示无限重连
2. report_status 后台执行不阻塞
"""

import asyncio
import time
from unittest.mock import AsyncMock
from beezer.plugins.outbounds.mqtt_websocket_outbound import MqttWebSocketOutbound


def test_infinite_reconnect_config():
    """测试无限重连配置"""
    print("1. 测试无限重连配置...")
    
    # 测试无限重连配置
    config_infinite = {
        "type": "mqtt_websocket",
        "id": "test_infinite",
        "broker_url": "ws://localhost:9001/mqtt",
        "max_reconnect_attempts": 0,  # 无限重连
        "topics": {}
    }
    
    plugin = MqttWebSocketOutbound(config_infinite)
    assert plugin.config.max_reconnect_attempts == 0
    print("   ✅ 无限重连配置正确")
    
    # 测试有限重连配置
    config_limited = {
        "type": "mqtt_websocket",
        "id": "test_limited",
        "broker_url": "ws://localhost:9001/mqtt",
        "max_reconnect_attempts": 5,  # 有限重连
        "topics": {}
    }
    
    plugin = MqttWebSocketOutbound(config_limited)
    assert plugin.config.max_reconnect_attempts == 5
    print("   ✅ 有限重连配置正确")


def test_reconnect_logic():
    """测试重连逻辑"""
    print("2. 测试重连逻辑...")
    
    # 测试无限重连逻辑
    config = {
        "type": "mqtt_websocket",
        "id": "test_logic",
        "broker_url": "ws://localhost:9001/mqtt",
        "max_reconnect_attempts": 0,
        "topics": {}
    }
    
    plugin = MqttWebSocketOutbound(config)
    
    # 模拟多次重连尝试
    plugin._reconnect_attempts = 100  # 模拟已经重连了100次
    
    # 检查是否应该继续重连（无限重连）
    should_continue = not (
        plugin.config.max_reconnect_attempts > 0 
        and plugin._reconnect_attempts >= plugin.config.max_reconnect_attempts
    )
    assert should_continue, "无限重连应该永远继续"
    print("   ✅ 无限重连逻辑正确")
    
    # 测试有限重连逻辑
    plugin.config.max_reconnect_attempts = 5
    plugin._reconnect_attempts = 6
    
    should_stop = (
        plugin.config.max_reconnect_attempts > 0 
        and plugin._reconnect_attempts >= plugin.config.max_reconnect_attempts
    )
    assert should_stop, "有限重连应该在达到次数后停止"
    print("   ✅ 有限重连逻辑正确")


async def test_background_status_reporting():
    """测试后台状态上报"""
    print("3. 测试后台状态上报...")
    
    config = {
        "type": "mqtt_websocket",
        "id": "test_background",
        "broker_url": "ws://localhost:9001/mqtt",
        "topics": {}
    }
    
    plugin = MqttWebSocketOutbound(config)
    
    # Mock report_status 方法
    call_count = 0
    async def mock_report_status(status, data):
        nonlocal call_count
        call_count += 1
        await asyncio.sleep(0.1)  # 模拟一些延迟
        print(f"   状态上报: {status} (调用 #{call_count})")
    
    plugin.report_status = mock_report_status
    
    # 测试后台状态上报不阻塞
    start_time = time.time()
    
    # 连续调用多次后台状态上报
    for i in range(3):
        plugin._report_status_background(f"test_{i}", {"data": i})
    
    end_time = time.time()
    elapsed = end_time - start_time
    
    print(f"   后台状态上报调用耗时: {elapsed:.3f}秒")
    assert elapsed < 0.05, f"后台状态上报应该立即返回，实际耗时: {elapsed:.3f}秒"
    print("   ✅ 后台状态上报不阻塞主流程")
    
    # 等待后台任务完成
    await asyncio.sleep(0.5)
    assert call_count == 3, f"应该调用3次状态上报，实际: {call_count}"
    print("   ✅ 后台状态上报任务正确执行")


def test_default_config():
    """测试默认配置"""
    print("4. 测试默认配置...")
    
    from beezer.type_model import MqttWebSocketOutboundConfig
    
    # 测试默认配置
    config_data = {
        "type": "mqtt_websocket",
        "id": "test_default",
        "broker_url": "ws://localhost:9001/mqtt"
    }
    
    config = MqttWebSocketOutboundConfig.model_validate(config_data)
    assert config.max_reconnect_attempts == 0, f"默认应该是无限重连，实际: {config.max_reconnect_attempts}"
    print("   ✅ 默认配置为无限重连")


async def main():
    """主测试函数"""
    print("MQTT WebSocket Outbound 改进功能验证")
    print("=" * 50)
    
    try:
        test_infinite_reconnect_config()
        test_reconnect_logic()
        await test_background_status_reporting()
        test_default_config()
        
        print("\n" + "=" * 50)
        print("🎉 所有改进功能验证通过！")
        print("\n改进总结:")
        print("✅ max_reconnect_attempts = 0 表示无限重连")
        print("✅ max_reconnect_attempts > 0 表示有限重连")
        print("✅ report_status 在后台执行，不阻塞主流程")
        print("✅ 默认配置为无限重连")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    import sys
    result = asyncio.run(main())
    sys.exit(result)
