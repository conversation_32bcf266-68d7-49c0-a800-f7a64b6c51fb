[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[unix_http_server]
file=/var/run/supervisor.sock

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:main]
command=/app/main
autostart=true
autorestart=true
numprocs=1
stdout_logfile=/var/log/supervisor/main.log
stderr_logfile=/var/log/supervisor/main_error.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10

[program:gui]
command=/app/gui
autostart=true
autorestart=true
numprocs=1
stdout_logfile=/var/log/supervisor/gui.log
stderr_logfile=/var/log/supervisor/gui_error.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
