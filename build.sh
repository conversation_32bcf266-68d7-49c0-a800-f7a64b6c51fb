#!/bin/bash

# 检查验证代码是否被注释
if grep -l "^[[:space:]]*#.*do_verify" beezer/flow_worker.py > /dev/null; then
    echo "错误：验证代码被注释，请取消注释后再构建"
    exit 1
fi
echo "开始构建..."
python3 -m nuitka --output-dir=./dist --follow-imports --no-pyi-file --python-flag=no_site --onefile --output-filename=main beezer/main.py
python3 -m nuitka --output-dir=./dist --follow-imports --no-pyi-file --python-flag=no_site --onefile --output-filename=worker beezer/flow_worker.py
python3 -m nuitka --output-dir=./dist --follow-imports --no-pyi-file --python-flag=no_site --onefile --output-filename=gui beezer/gui/__main__.py

docker build -t ibee/beezer:latest -f Dockerfile .
docker save -o beezer-image.tar ibee/beezer:latest

