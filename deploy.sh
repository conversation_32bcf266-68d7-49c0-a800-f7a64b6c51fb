#!/bin/bash
sed -i 's/http:\/\/archive.ubuntu.com/http:\/\/mirrors.aliyun.com/g' /etc/apt/sources.list.d/ubuntu.sources && \
sed -i 's/http:\/\/security.ubuntu.com/http:\/\/mirrors.aliyun.com/g' /etc/apt/sources.list.d/ubuntu.sources

apt-get update
apt-get install docker.io docker-compose -y
apt install python3-setuptools -y
# apt-get install aria2 -y
docker load -i beezer-image.tar
mkdir /root/.config/beezer -p
docker-compose -f docker-compose.yml up -d
# docker cp wine_env.tar.gz beezer:/app
# docker exec beezer tar zxvf wine_env.tar.gz --owner=root --group=root -C /app/   # mitsubishi syntec
# docker exec beezer chown -R root:root /app/.wine