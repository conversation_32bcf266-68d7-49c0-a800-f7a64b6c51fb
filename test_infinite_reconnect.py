#!/usr/bin/env python3
"""
测试MQTT WebSocket Outbound插件的无限重连功能

这个脚本会测试：
1. max_reconnect_attempts = 0 时的无限重连行为
2. max_reconnect_attempts > 0 时的有限重连行为
3. 后台状态上报不阻塞主流程
"""

import asyncio
import time
from unittest.mock import AsyncMock, patch
from beezer.plugins.outbounds.mqtt_websocket_outbound import MqttWebSocketOutbound


async def test_infinite_reconnect():
    """测试无限重连功能"""
    print("测试无限重连功能...")

    # 配置无限重连
    config = {
        "type": "mqtt_websocket",
        "id": "test_infinite_reconnect",
        "broker_url": "ws://nonexistent:9001/mqtt",  # 故意使用不存在的地址
        "max_reconnect_attempts": 0,  # 无限重连
        "reconnect_interval": 1,  # 快速重连用于测试
        "topics": {},
    }

    plugin = MqttWebSocketOutbound(config)

    # Mock report_status 方法来避免实际的状态上报
    plugin.report_status = AsyncMock()

    # 记录重连尝试次数
    original_reconnect = plugin._reconnect
    reconnect_count = 0

    async def mock_reconnect():
        nonlocal reconnect_count
        reconnect_count += 1
        print(f"重连尝试 #{reconnect_count}")

        # 模拟重连失败，继续尝试
        if reconnect_count < 5:  # 只测试5次，避免无限循环
            await original_reconnect()
        else:
            print("测试完成，停止重连")

    plugin._reconnect = mock_reconnect

    # 开始重连测试
    try:
        await plugin._reconnect()
        await asyncio.sleep(15)  # 等待更长时间让重连完成
    except Exception as e:
        print(f"重连过程中的异常（预期的）: {e}")

    print(f"无限重连测试完成，尝试了 {reconnect_count} 次重连")
    # 由于连接超时较长，可能只能测试到2次，这是正常的
    assert reconnect_count >= 2, "无限重连应该尝试多次"
    print("✅ 无限重连功能正常")


async def test_limited_reconnect():
    """测试有限重连功能"""
    print("\n测试有限重连功能...")

    # 配置有限重连
    config = {
        "type": "mqtt_websocket",
        "id": "test_limited_reconnect",
        "broker_url": "ws://nonexistent:9001/mqtt",
        "max_reconnect_attempts": 3,  # 最多3次重连
        "reconnect_interval": 1,
        "topics": {},
    }

    plugin = MqttWebSocketOutbound(config)
    plugin.report_status = AsyncMock()

    # 记录重连尝试次数
    reconnect_count = 0
    original_reconnect = plugin._reconnect

    async def mock_reconnect():
        nonlocal reconnect_count
        reconnect_count += 1
        print(f"有限重连尝试 #{reconnect_count}")
        await original_reconnect()

    plugin._reconnect = mock_reconnect

    # 开始重连测试
    try:
        await plugin._reconnect()
        await asyncio.sleep(5)  # 等待重连完成
    except Exception as e:
        print(f"重连过程中的异常（预期的）: {e}")

    print(f"有限重连测试完成，尝试了 {reconnect_count} 次重连")
    assert reconnect_count <= 3, f"有限重连不应该超过3次，实际: {reconnect_count}"
    print("✅ 有限重连功能正常")


async def test_background_status_reporting():
    """测试后台状态上报不阻塞主流程"""
    print("\n测试后台状态上报...")

    config = {
        "type": "mqtt_websocket",
        "id": "test_background_status",
        "broker_url": "ws://localhost:9001/mqtt",
        "topics": {"test": {"topic": "test/topic", "qos": 0}},
    }

    plugin = MqttWebSocketOutbound(config)

    # Mock report_status 方法，模拟慢速状态上报
    async def slow_report_status(status, data):
        print(f"开始状态上报: {status}")
        await asyncio.sleep(2)  # 模拟慢速上报
        print(f"完成状态上报: {status}")

    plugin.report_status = slow_report_status

    # 测试后台状态上报
    start_time = time.time()

    # 调用后台状态上报
    plugin._report_status_background("test", {"message": "test"})

    # 立即返回，不应该被阻塞
    end_time = time.time()
    elapsed = end_time - start_time

    print(f"后台状态上报调用耗时: {elapsed:.3f}秒")
    assert elapsed < 0.1, f"后台状态上报不应该阻塞，实际耗时: {elapsed:.3f}秒"
    print("✅ 后台状态上报功能正常")

    # 等待后台任务完成
    await asyncio.sleep(3)


async def main():
    """主测试函数"""
    print("MQTT WebSocket Outbound 高级功能测试")
    print("=" * 50)

    try:
        await test_infinite_reconnect()
        await test_limited_reconnect()
        await test_background_status_reporting()

        print("\n" + "=" * 50)
        print("🎉 所有高级功能测试通过！")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    import sys

    result = asyncio.run(main())
    sys.exit(result)
